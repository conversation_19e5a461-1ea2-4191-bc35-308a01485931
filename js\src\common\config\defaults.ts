import type { RootConfig, Environment } from './types';
import {
  EXTENSION_CONFIG,
  TIMING,
  DOM_ELEMENTS,
  CSS_CLASSES
} from './constants';

export const defaultConfig: RootConfig = {
  env: (process.env.NODE_ENV as Environment) || 'production',
  app: {
    extensionId: EXTENSION_CONFIG.ID,
    translationPrefix: EXTENSION_CONFIG.TRANSLATION_PREFIX,
  },
  slider: {
    maxSlides: EXTENSION_CONFIG.MAX_SLIDES,
    defaultTransitionTime: TIMING.DEFAULT_TRANSITION_TIME,
    checkTime: TIMING.CHECK_INTERVAL,
    dataCheckInterval: TIMING.DATA_CHECK_INTERVAL,
    dom: {
      containerId: DOM_ELEMENTS.SPLIDE_AD_CONTAINER_ID,
      splideClass: CSS_CLASSES.AD_SPLIDE,
    },
    splide: {
      gap: '15px',
      type: 'loop',
      focus: 'center',
      perPage: 1, // 显示1个完整幻灯片
      pagination: true,
      arrows: true,
      enableAutoplay: true, // Enable autoplay by default
    },
  },
  ui: {
    headerIconId: DOM_ELEMENTS.HEADER_ICON_ID,
    headerIconUrl: EXTENSION_CONFIG.HEADER_ICON_URL,
  },
};
