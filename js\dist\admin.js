(function(i,E,h,e){"use strict";const d={SAVE_DEBOUNCE_DELAY:500,DEFAULT_MAX_SLIDES:30,EMPTY_SLIDES_COUNT:0},u=1,D=1,v=-1,l=1;class _ extends E{constructor(){super(...arguments),this.slides=[],this.loading=!1,this.nextId=u,this.timeouts={}}oninit(t){super.oninit(t),this.loadExistingSlides()}loadExistingSlides(){const{extensionId:t,maxSlides:r=d.DEFAULT_MAX_SLIDES}=this.attrs,n=[];for(let s=u;s<=r;s+=l){const a=`${t}.Link${s}`,o=`${t}.Image${s}`,g=i.data.settings[a]||"",I=i.data.settings[o]||"";(g||I)&&(n.push({id:s,link:g,image:I}),this.nextId=Math.max(this.nextId,s+l))}this.slides=n,n.length===d.EMPTY_SLIDES_COUNT&&this.addSlide()}addSlide(){const t={id:this.nextId,link:"",image:""};this.nextId+=1,this.slides.push(t),e.redraw()}removeSlide(t){const{extensionId:r}=this.attrs,n=this.slides.findIndex(a=>a.id===t);if(n===v)return;const s=this.slides[n];this.saveSetting(`${r}.Link${s.id}`,""),this.saveSetting(`${r}.Image${s.id}`,""),this.slides.splice(n,l),this.slides.length===d.EMPTY_SLIDES_COUNT&&this.addSlide(),e.redraw()}updateSlide(t,r,n){const{extensionId:s}=this.attrs,a=this.slides.find(g=>g.id===t);if(!a)return;a[r]=n;let o="";r==="link"?o=`${s}.Link${a.id}`:o=`${s}.Image${a.id}`,this.saveSetting(o,n)}saveSetting(t,r){const n=`saveTimeout_${t}`;clearTimeout(this.timeouts[n]),this.timeouts[n]=setTimeout(()=>{i.data.settings[t]=r,i.request({method:"POST",url:i.forum.attribute("apiUrl")+"/settings",body:{[t]:r}}).catch(()=>{})},d.SAVE_DEBOUNCE_DELAY)}view(){return e("div.Form-group",[e("label.FormLabel",i.translator.trans("wusong8899-header-advertisement.admin.SlideSettings")),e("div.helpText",i.translator.trans("wusong8899-header-advertisement.admin.SlideSettingsHelp")),e("div.DynamicSlideSettings",[this.slides.map((t,r)=>this.renderSlide(t,r)),e("div.DynamicSlideSettings-addButton",[e(h,{className:"Button Button--primary",icon:"fas fa-plus",onclick:()=>this.addSlide()},i.translator.trans("wusong8899-header-advertisement.admin.AddSlide"))])])])}renderSlide(t,r){return e("div.DynamicSlideSettings-slide",{key:t.id},[e("div.DynamicSlideSettings-slideHeader",[e("h4",i.translator.trans("wusong8899-header-advertisement.admin.SlideNumber",{number:r+l})),e(h,{className:"Button Button--danger",icon:"fas fa-trash",onclick:()=>this.removeSlide(t.id),disabled:this.slides.length===D},i.translator.trans("wusong8899-header-advertisement.admin.DeleteSlide"))]),e("div.DynamicSlideSettings-slideFields",[e("div.Form-group",[e("label.FormLabel",i.translator.trans("wusong8899-header-advertisement.admin.SlideLink")),e("input.FormControl",{type:"url",placeholder:"https://example.com",value:t.link,oninput:n=>{const s=n.target;this.updateSlide(t.id,"link",s.value)}})]),e("div.Form-group",[e("label.FormLabel",i.translator.trans("wusong8899-header-advertisement.admin.SlideImage")),e("input.FormControl",{type:"url",placeholder:"https://example.com/image.jpg",value:t.image,oninput:n=>{const s=n.target;this.updateSlide(t.id,"image",s.value)}})])])])}}class T{constructor(t){this.extensionId=t,this.extensionData=i.extensionData.for(t)}registerTransitionTimeSetting(){return this.extensionData.registerSetting({setting:`${this.extensionId}.TransitionTime`,type:"number",label:String(i.translator.trans("wusong8899-header-advertisement.admin.TransitionTime"))}),this}registerAutoplaySetting(){return this.extensionData.registerSetting({setting:`${this.extensionId}.EnableAutoplay`,type:"boolean",label:String(i.translator.trans("wusong8899-header-advertisement.admin.EnableAutoplay")),help:String(i.translator.trans("wusong8899-header-advertisement.admin.EnableAutoplayHelp"))}),this}registerHeaderIconUrlSetting(){return this.extensionData.registerSetting({setting:`${this.extensionId}.HeaderIconUrl`,type:"url",label:String(i.translator.trans("wusong8899-header-advertisement.admin.HeaderIconUrl")),help:String(i.translator.trans("wusong8899-header-advertisement.admin.HeaderIconUrlHelp"))}),this}registerSlideSettings(t=d.DEFAULT_MAX_SLIDES){return this.extensionData.registerSetting(()=>e(_,{extensionId:this.extensionId,maxSlides:t})),this}registerAllSettings(t=d.DEFAULT_MAX_SLIDES){return this.registerTransitionTimeSetting().registerAutoplaySetting().registerHeaderIconUrlSetting().registerSlideSettings(t)}}const c={EXTENSION_ID:"wusong8899-header-advertisement",MAX_SLIDES:d.DEFAULT_MAX_SLIDES},x=(S=c.EXTENSION_ID,t=c.MAX_SLIDES)=>{new T(S).registerAllSettings(t)};i.initializers.add("wusong8899-header-advertisement",()=>{x()})})(flarum.core.compat["admin/app"],flarum.core.compat["common/Component"],flarum.core.compat["common/components/Button"],m);
//# sourceMappingURL=admin.js.map

module.exports={};